import json
import logging
import os
import math
from datetime import datetime
from typing import Any, Dict, List, Optional

import portalocker
from flask import Blueprint, jsonify, request
from pydantic import BaseModel, Field

from src.utils.errors import error_response

audit_bp = Blueprint('audit', __name__)
logger = logging.getLogger(__name__)

# Base directory for backend data (same as where reports/documents live)
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
AUDIT_LOG_PATH = os.path.join(BASE_DIR, 'audit.log')


class GetAuditLogsQuery(BaseModel):
    search: Optional[str] = Field(default='', description='Search term for filtering by event type or resource ID')
    limit: Optional[int] = Field(default=50, ge=1, le=1000, description='Number of results per page')
    page: Optional[int] = Field(default=1, ge=1, description='Page number (1-based)')
    sort: Optional[str] = Field(default='desc', regex='^(asc|desc)$', description='Sort order for timestamp')


def _read_audit_logs_safely() -> List[Dict[str, Any]]:
    """Read audit logs from file with concurrency-safe locking.
    
    Returns an empty list if the file doesn't exist or can't be read.
    """
    if not os.path.exists(AUDIT_LOG_PATH):
        return []
    
    try:
        # Use shared lock for reading
        with portalocker.Lock(AUDIT_LOG_PATH, mode='r', flags=portalocker.LOCK_SH, timeout=5) as locked_file:
            logs = []
            for line in locked_file:
                line = line.strip()
                if line:
                    try:
                        log_entry = json.loads(line)
                        logs.append(log_entry)
                    except json.JSONDecodeError:
                        # Skip invalid JSON lines
                        continue
            return logs
    except Exception as exc:
        logger.warning('Failed to read audit logs: %s', exc)
        return []


def _filter_logs(logs: List[Dict[str, Any]], search_term: str) -> List[Dict[str, Any]]:
    """Filter logs based on search term matching event type or resource ID."""
    if not search_term:
        return logs
    
    search_lower = search_term.lower()
    filtered_logs = []
    
    for log in logs:
        # Search in action (event type)
        if search_lower in log.get('action', '').lower():
            filtered_logs.append(log)
            continue
            
        # Search in details for resource IDs
        details = log.get('details', {})
        if isinstance(details, dict):
            # Check common resource ID fields
            resource_fields = ['report_id', 'template_id', 'user_id', 'filename']
            for field in resource_fields:
                if field in details and search_lower in str(details[field]).lower():
                    filtered_logs.append(log)
                    break
    
    return filtered_logs


def _sort_logs(logs: List[Dict[str, Any]], sort_order: str) -> List[Dict[str, Any]]:
    """Sort logs by timestamp."""
    reverse = sort_order == 'desc'
    try:
        return sorted(logs, key=lambda x: x.get('timestamp', ''), reverse=reverse)
    except Exception:
        # If sorting fails, return logs as-is
        return logs


def _paginate_logs(logs: List[Dict[str, Any]], page: int, limit: int) -> Dict[str, Any]:
    """Paginate logs and return pagination metadata."""
    total = len(logs)
    total_pages = math.ceil(total / limit) if total > 0 else 1
    start_idx = (page - 1) * limit
    end_idx = start_idx + limit
    
    paginated_logs = logs[start_idx:end_idx]
    
    return {
        'total': total,
        'page': page,
        'pages': total_pages,
        'results': paginated_logs
    }


@audit_bp.route('/audit/logs', methods=['GET'])
def get_audit_logs():
    """Get paginated audit logs with search and sorting.
    
    Query parameters:
    - search: Filter by event type or resource ID (default: '')
    - limit: Number of results per page (default: 50, max: 1000)
    - page: Page number, 1-based (default: 1)
    - sort: Sort order 'asc' or 'desc' (default: 'desc')
    
    Returns:
    {
        "total": 150,
        "page": 1,
        "pages": 3,
        "results": [
            {
                "timestamp": "2025-01-06T10:30:00Z",
                "action": "report_generated",
                "user": "anonymous",
                "details": {
                    "report_id": "abc-123",
                    "template_id": "def-456"
                }
            },
            ...
        ]
    }
    """
    try:
        # Validate query parameters
        args_model = GetAuditLogsQuery.model_validate(request.args.to_dict())
        
        # Read audit logs from file
        logs = _read_audit_logs_safely()
        
        # Apply search filter
        if args_model.search:
            logs = _filter_logs(logs, args_model.search)
        
        # Sort logs
        logs = _sort_logs(logs, args_model.sort)
        
        # Paginate results
        result = _paginate_logs(logs, args_model.page, args_model.limit)
        
        return jsonify(result)
        
    except ValueError as e:
        return error_response(400, f'Invalid query parameters: {str(e)}')
    except Exception as e:
        logger.exception("Failed to retrieve audit logs: %s", str(e))
        return error_response(500, f'Failed to retrieve audit logs: {str(e)}')


if __name__ == '__main__':
    # Test the audit logs endpoint locally
    from src.utils.audit import audit_event

    # Create some test audit events
    print('Creating test audit events...')
    audit_event('test_upload', 'user1', {'template_id': 'template-123', 'filename': 'test.docx'})
    audit_event('report_generated', 'user2', {'report_id': 'report-456', 'template_id': 'template-123'})
    audit_event('report_downloaded', 'user1', {'report_id': 'report-456', 'format': 'pdf'})
    audit_event('template_deleted', 'admin', {'template_id': 'template-789'})

    # Test reading logs
    print('\nReading audit logs...')
    logs = _read_audit_logs_safely()
    print(f'Found {len(logs)} log entries')

    # Test filtering
    print('\nTesting search filter...')
    filtered = _filter_logs(logs, 'report')
    print(f'Found {len(filtered)} entries matching "report"')

    # Test pagination
    print('\nTesting pagination...')
    paginated = _paginate_logs(logs, 1, 2)
    print(f'Page 1 of {paginated["pages"]}: {len(paginated["results"])} results')
